const { Client } = require('pg');
require('dotenv').config();

async function createMissingTables() {
  console.log('🔄 Creating missing HIPAA tables...');
  
  const client = new Client({
    connectionString: process.env.DATABASE_URL || 'postgresql://complyuser:complypassword@localhost:5432/complychecker_dev'
  });

  try {
    await client.connect();
    console.log('✅ Connected to database');

    // Check which tables are missing
    const requiredTables = [
      'hipaa_scans',
      'hipaa_check_results',
      'hipaa_findings',
      'hipaa_analysis_levels',
      'hipaa_remediation',
      'hipaa_evidence',
      'hipaa_recommendations'
    ];

    const checkTables = await client.query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      AND table_name = ANY($1)
      ORDER BY table_name
    `, [requiredTables]);

    const existingTables = checkTables.rows.map(row => row.table_name);
    const missingTables = requiredTables.filter(table => !existingTables.includes(table));

    console.log('📊 Existing tables:', existingTables);
    console.log('❌ Missing tables:', missingTables);

    // Create hipaa_scans table if missing
    if (missingTables.includes('hipaa_scans')) {
      console.log('🔧 Creating hipaa_scans table...');
      await client.query(`
        CREATE TABLE hipaa_scans (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          scan_id UUID NOT NULL,
          target_url TEXT NOT NULL,
          overall_score INTEGER NOT NULL,
          overall_passed BOOLEAN NOT NULL,
          compliance_level TEXT NOT NULL,
          analysis_levels_used JSONB NOT NULL,
          processing_time_ms INTEGER NOT NULL,
          total_checks INTEGER NOT NULL,
          passed_checks INTEGER NOT NULL,
          failed_checks INTEGER NOT NULL,
          critical_issues INTEGER DEFAULT 0,
          high_issues INTEGER DEFAULT 0,
          medium_issues INTEGER DEFAULT 0,
          low_issues INTEGER DEFAULT 0,
          scan_options JSONB,
          cache_hits INTEGER DEFAULT 0,
          errors JSONB,
          warnings JSONB,
          user_agent TEXT,
          scan_version TEXT DEFAULT '2.0',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `);
      console.log('✅ hipaa_scans table created');
    } else {
      console.log('✅ hipaa_scans table already exists');
    }

    // Create hipaa_check_results table if missing
    if (missingTables.includes('hipaa_check_results')) {
      console.log('🔧 Creating hipaa_check_results table...');
      await client.query(`
        CREATE TABLE hipaa_check_results (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          hipaa_scan_id UUID NOT NULL,
          check_id TEXT NOT NULL,
          name TEXT NOT NULL,
          category TEXT NOT NULL,
          passed BOOLEAN NOT NULL,
          severity TEXT NOT NULL,
          confidence INTEGER NOT NULL,
          description TEXT NOT NULL,
          overall_score INTEGER,
          processing_time_ms INTEGER NOT NULL,
          check_version TEXT DEFAULT '2.0',
          analysis_levels_executed JSONB,
          errors JSONB,
          warnings JSONB,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `);
      console.log('✅ hipaa_check_results table created');
    } else {
      console.log('✅ hipaa_check_results table already exists');
    }

    // Create hipaa_findings table if missing
    if (missingTables.includes('hipaa_findings')) {
      console.log('🔧 Creating hipaa_findings table...');
      await client.query(`
        CREATE TABLE hipaa_findings (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          check_result_id UUID NOT NULL,
          type TEXT NOT NULL,
          location TEXT NOT NULL,
          content TEXT NOT NULL,
          severity TEXT NOT NULL,
          message TEXT NOT NULL,
          confidence INTEGER NOT NULL,
          context TEXT,
          suggestion TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `);
      console.log('✅ hipaa_findings table created');
    } else {
      console.log('✅ hipaa_findings table already exists');
    }

    // Create hipaa_analysis_levels table if missing
    if (missingTables.includes('hipaa_analysis_levels')) {
      console.log('🔧 Creating hipaa_analysis_levels table...');
      await client.query(`
        CREATE TABLE hipaa_analysis_levels (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          check_result_id UUID NOT NULL,
          level INTEGER NOT NULL,
          method TEXT NOT NULL,
          score INTEGER NOT NULL,
          confidence INTEGER NOT NULL,
          processing_time_ms INTEGER NOT NULL,
          findings_count INTEGER DEFAULT 0,
          errors JSONB,
          warnings JSONB,
          metadata JSONB,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `);
      console.log('✅ hipaa_analysis_levels table created');
    } else {
      console.log('✅ hipaa_analysis_levels table already exists');
    }

    // Create hipaa_remediation table if missing
    if (missingTables.includes('hipaa_remediation')) {
      console.log('🔧 Creating hipaa_remediation table...');
      await client.query(`
        CREATE TABLE hipaa_remediation (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          check_result_id UUID NOT NULL,
          priority TEXT NOT NULL,
          effort TEXT NOT NULL,
          steps JSONB NOT NULL,
          resources JSONB NOT NULL,
          timeline TEXT NOT NULL,
          estimated_cost TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `);
      console.log('✅ hipaa_remediation table created');
    } else {
      console.log('✅ hipaa_remediation table already exists');
    }

    // Create hipaa_evidence table if missing
    if (missingTables.includes('hipaa_evidence')) {
      console.log('🔧 Creating hipaa_evidence table...');
      await client.query(`
        CREATE TABLE hipaa_evidence (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          check_result_id UUID NOT NULL,
          type TEXT NOT NULL,
          content TEXT NOT NULL,
          location TEXT NOT NULL,
          relevance INTEGER NOT NULL,
          evidence_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `);
      console.log('✅ hipaa_evidence table created');
    } else {
      console.log('✅ hipaa_evidence table already exists');
    }

    // Create hipaa_recommendations table if missing
    if (missingTables.includes('hipaa_recommendations')) {
      console.log('🔧 Creating hipaa_recommendations table...');
      await client.query(`
        CREATE TABLE hipaa_recommendations (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          hipaa_scan_id UUID NOT NULL,
          recommendation_id TEXT NOT NULL,
          priority TEXT NOT NULL,
          title TEXT NOT NULL,
          description TEXT NOT NULL,
          category TEXT NOT NULL,
          effort TEXT NOT NULL,
          impact TEXT NOT NULL,
          timeline TEXT NOT NULL,
          resources JSONB,
          related_checks JSONB,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `);
      console.log('✅ hipaa_recommendations table created');
    } else {
      console.log('✅ hipaa_recommendations table already exists');
    }

    // Create indexes for performance
    console.log('🔧 Creating indexes...');
    
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_hipaa_scans_scan_id ON hipaa_scans(scan_id);
    `);
    
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_hipaa_check_results_hipaa_scan_id ON hipaa_check_results(hipaa_scan_id);
    `);
    
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_hipaa_check_results_check_id ON hipaa_check_results(check_id);
    `);

    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_hipaa_findings_check_result_id ON hipaa_findings(check_result_id);
    `);

    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_hipaa_analysis_levels_check_result_id ON hipaa_analysis_levels(check_result_id);
    `);

    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_hipaa_remediation_check_result_id ON hipaa_remediation(check_result_id);
    `);

    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_hipaa_evidence_check_result_id ON hipaa_evidence(check_result_id);
    `);

    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_hipaa_recommendations_hipaa_scan_id ON hipaa_recommendations(hipaa_scan_id);
    `);

    console.log('✅ Indexes created');

    // Verify tables now exist
    const finalCheck = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name LIKE 'hipaa_%'
      ORDER BY table_name
    `);

    console.log('🎉 Final verification - HIPAA tables:');
    finalCheck.rows.forEach(row => {
      console.log(`   ✅ ${row.table_name}`);
    });

    console.log('🎉 All missing tables created successfully!');

  } catch (error) {
    console.error('❌ Error creating tables:', error);
    throw error;
  } finally {
    await client.end();
  }
}

// Run immediately
createMissingTables()
  .then(() => {
    console.log('✅ Setup complete! You can now run privacy scans.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  });
