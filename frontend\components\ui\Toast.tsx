'use client';

/**
 * Toast Notification System
 * Provides accessible toast notifications with auto-dismiss and screen reader support
 */

import React, { createContext, useContext, useReducer, useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { X, CheckCircle, AlertCircle, AlertTriangle, Info } from 'lucide-react';
import { announceToScreenReader } from '@/utils/accessibility';

// ===== TYPES =====

export type ToastType = 'success' | 'error' | 'warning' | 'info';

export interface Toast {
  id: string;
  type: ToastType;
  title: string;
  message?: string;
  duration?: number;
  persistent?: boolean;
  action?: {
    label: string;
    onClick: () => void;
  };
}

interface ToastState {
  toasts: Toast[];
}

type ToastAction =
  | { type: 'ADD_TOAST'; toast: Toast }
  | { type: 'REMOVE_TOAST'; id: string }
  | { type: 'CLEAR_ALL' };

// ===== CONTEXT =====

interface ToastContextValue {
  toasts: Toast[];
  addToast: (toast: Omit<Toast, 'id'>) => string;
  removeToast: (id: string) => void;
  clearAll: () => void;
  success: (title: string, message?: string, options?: Partial<Toast>) => string;
  error: (title: string, message?: string, options?: Partial<Toast>) => string;
  warning: (title: string, message?: string, options?: Partial<Toast>) => string;
  info: (title: string, message?: string, options?: Partial<Toast>) => string;
}

const ToastContext = createContext<ToastContextValue | undefined>(undefined);

// ===== REDUCER =====

function toastReducer(state: ToastState, action: ToastAction): ToastState {
  switch (action.type) {
    case 'ADD_TOAST':
      return {
        ...state,
        toasts: [...state.toasts, action.toast],
      };
    case 'REMOVE_TOAST':
      return {
        ...state,
        toasts: state.toasts.filter((toast) => toast.id !== action.id),
      };
    case 'CLEAR_ALL':
      return {
        ...state,
        toasts: [],
      };
    default:
      return state;
  }
}

// ===== PROVIDER =====

interface ToastProviderProps {
  children: React.ReactNode;
  maxToasts?: number;
}

export function ToastProvider({ children, maxToasts = 5 }: ToastProviderProps) {
  const [state, dispatch] = useReducer(toastReducer, { toasts: [] });

  const generateId = () => `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

  const addToast = (toast: Omit<Toast, 'id'>) => {
    const id = generateId();
    const newToast: Toast = {
      id,
      duration: 5000,
      persistent: false,
      ...toast,
    };

    // Remove oldest toast if at max capacity
    if (state.toasts.length >= maxToasts) {
      dispatch({ type: 'REMOVE_TOAST', id: state.toasts[0].id });
    }

    dispatch({ type: 'ADD_TOAST', toast: newToast });

    // Announce to screen readers
    const announcement = `${newToast.type} notification: ${newToast.title}${
      newToast.message ? `. ${newToast.message}` : ''
    }`;
    announceToScreenReader(announcement, 'assertive');

    return id;
  };

  const removeToast = (id: string) => {
    dispatch({ type: 'REMOVE_TOAST', id });
  };

  const clearAll = () => {
    dispatch({ type: 'CLEAR_ALL' });
  };

  const success = (title: string, message?: string, options?: Partial<Toast>) => {
    return addToast({ type: 'success', title, message, ...options });
  };

  const error = (title: string, message?: string, options?: Partial<Toast>) => {
    return addToast({
      type: 'error',
      title,
      message,
      persistent: true,
      ...options,
    });
  };

  const warning = (title: string, message?: string, options?: Partial<Toast>) => {
    return addToast({ type: 'warning', title, message, ...options });
  };

  const info = (title: string, message?: string, options?: Partial<Toast>) => {
    return addToast({ type: 'info', title, message, ...options });
  };

  const value: ToastContextValue = {
    toasts: state.toasts,
    addToast,
    removeToast,
    clearAll,
    success,
    error,
    warning,
    info,
  };

  return (
    <ToastContext.Provider value={value}>
      {children}
      <ToastContainer />
    </ToastContext.Provider>
  );
}

// ===== HOOK =====

export function useToast() {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
}

// ===== COMPONENTS =====

function ToastContainer() {
  const { toasts } = useToast();
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Don't render anything during SSR or before hydration
  if (!isMounted || typeof document === 'undefined') return null;

  return createPortal(
    <div
      className="fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full"
      aria-live="assertive"
      aria-label="Notifications"
    >
      {toasts.map((toast) => (
        <ToastItem key={toast.id} toast={toast} />
      ))}
    </div>,
    document.body,
  );
}

interface ToastItemProps {
  toast: Toast;
}

function ToastItem({ toast }: ToastItemProps) {
  const { removeToast } = useToast();

  // Auto-dismiss non-persistent toasts
  useEffect(() => {
    if (!toast.persistent && toast.duration) {
      const timer = setTimeout(() => {
        removeToast(toast.id);
      }, toast.duration);

      return () => clearTimeout(timer);
    }
  }, [toast.id, toast.persistent, toast.duration, removeToast]);

  const getIcon = () => {
    switch (toast.type) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-600" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
      case 'info':
        return <Info className="h-5 w-5 text-blue-600" />;
    }
  };

  const getStyles = () => {
    switch (toast.type) {
      case 'success':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'error':
        return 'bg-red-50 border-red-200 text-red-800';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case 'info':
        return 'bg-blue-50 border-blue-200 text-blue-800';
    }
  };

  return (
    <div
      className={`
        relative p-4 border rounded-lg shadow-lg animate-slide-in-right
        ${getStyles()}
      `}
      role="alert"
      aria-live="assertive"
    >
      <div className="flex items-start gap-3">
        <div className="flex-shrink-0" aria-hidden="true">
          {getIcon()}
        </div>

        <div className="flex-1 min-w-0">
          <h4 className="font-medium text-sm">{toast.title}</h4>
          {toast.message && <p className="mt-1 text-sm opacity-90">{toast.message}</p>}

          {toast.action && (
            <div className="mt-3">
              <button
                onClick={toast.action.onClick}
                className="text-sm font-medium underline hover:no-underline focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current rounded"
              >
                {toast.action.label}
              </button>
            </div>
          )}
        </div>

        <button
          onClick={() => removeToast(toast.id)}
          className="flex-shrink-0 p-1 rounded-md hover:bg-black hover:bg-opacity-10 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current"
          aria-label="Dismiss notification"
        >
          <X className="h-4 w-4" />
        </button>
      </div>

      {/* Progress bar for auto-dismiss */}
      {!toast.persistent && toast.duration && (
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-black bg-opacity-10 rounded-b-lg overflow-hidden">
          <div
            className="h-full bg-current opacity-50 animate-progress"
            style={{
              animationDuration: `${toast.duration}ms`,
              animationTimingFunction: 'linear',
            }}
          />
        </div>
      )}
    </div>
  );
}

// ===== UTILITY FUNCTIONS =====

/**
 * Show a success toast
 */
export function showSuccess(title: string, message?: string, options?: Partial<Toast>) {
  // This is a utility function that can be used outside of React components
  // It requires the ToastProvider to be mounted somewhere in the app
  const event = new CustomEvent('show-toast', {
    detail: { type: 'success', title, message, ...options },
  });
  window.dispatchEvent(event);
}

/**
 * Show an error toast
 */
export function showError(title: string, message?: string, options?: Partial<Toast>) {
  const event = new CustomEvent('show-toast', {
    detail: { type: 'error', title, message, persistent: true, ...options },
  });
  window.dispatchEvent(event);
}

/**
 * Show a warning toast
 */
export function showWarning(title: string, message?: string, options?: Partial<Toast>) {
  const event = new CustomEvent('show-toast', {
    detail: { type: 'warning', title, message, ...options },
  });
  window.dispatchEvent(event);
}

/**
 * Show an info toast
 */
export function showInfo(title: string, message?: string, options?: Partial<Toast>) {
  const event = new CustomEvent('show-toast', {
    detail: { type: 'info', title, message, ...options },
  });
  window.dispatchEvent(event);
}

// ===== TOAST LISTENER =====

/**
 * Hook to listen for toast events from utility functions
 */
export function useToastListener() {
  const { addToast } = useToast();

  useEffect(() => {
    const handleToastEvent = (event: CustomEvent) => {
      addToast(event.detail);
    };

    window.addEventListener('show-toast', handleToastEvent as EventListener);
    return () => {
      window.removeEventListener('show-toast', handleToastEvent as EventListener);
    };
  }, [addToast]);
}

// Export all components and hooks
export default ToastProvider;
