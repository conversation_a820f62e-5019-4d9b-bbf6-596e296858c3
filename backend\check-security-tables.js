const { Client } = require('pg');
require('dotenv').config();

async function checkSecurityTables() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL || 'postgresql://complyuser:complypassword@localhost:5432/complychecker_dev'
  });

  try {
    await client.connect();
    console.log('✅ Connected to database');

    // Check security tables
    const tables = ['hipaa_security_scans', 'hipaa_security_test_results', 'hipaa_security_vulnerabilities'];
    
    for (const table of tables) {
      try {
        const result = await client.query(`SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = '${table}')`);
        console.log(`📋 ${table}: ${result.rows[0].exists ? '✅ EXISTS' : '❌ MISSING'}`);
        
        if (result.rows[0].exists) {
          const count = await client.query(`SELECT COUNT(*) FROM ${table}`);
          console.log(`   📊 Records: ${count.rows[0].count}`);
        }
      } catch (error) {
        console.log(`❌ ${table}: ERROR - ${error.message}`);
      }
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await client.end();
  }
}

checkSecurityTables();
