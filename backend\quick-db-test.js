const { Client } = require('pg');
require('dotenv').config();

async function quickTest() {
  console.log('🔄 Quick database test...');
  
  const client = new Client({
    connectionString: process.env.DATABASE_URL || 'postgresql://complyuser:complypassword@localhost:5432/complychecker_dev'
  });

  try {
    await client.connect();
    console.log('✅ Connected to database');

    // Check existing tables
    const tables = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name LIKE 'hipaa_%'
      ORDER BY table_name
    `);

    console.log('📊 Existing HIPAA tables:');
    tables.rows.forEach(row => {
      console.log(`   - ${row.table_name}`);
    });

    // Check if hipaa_check_results exists
    const checkResults = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'hipaa_check_results'
      );
    `);

    if (!checkResults.rows[0].exists) {
      console.log('❌ hipaa_check_results table missing - this is the issue!');
      
      // Create the minimal required table
      console.log('🔧 Creating hipaa_check_results table...');
      await client.query(`
        CREATE TABLE hipaa_check_results (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          hipaa_scan_id UUID NOT NULL,
          check_id TEXT NOT NULL,
          name TEXT NOT NULL,
          category TEXT NOT NULL,
          passed BOOLEAN NOT NULL,
          severity TEXT NOT NULL,
          confidence INTEGER NOT NULL,
          description TEXT NOT NULL,
          overall_score INTEGER,
          processing_time_ms INTEGER NOT NULL,
          check_version TEXT DEFAULT '2.0',
          analysis_levels_executed JSONB,
          errors JSONB,
          warnings JSONB,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `);
      console.log('✅ hipaa_check_results table created!');
    } else {
      console.log('✅ hipaa_check_results table exists');
    }

    // Check if hipaa_scans exists
    const hipaaScans = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'hipaa_scans'
      );
    `);

    if (!hipaaScans.rows[0].exists) {
      console.log('❌ hipaa_scans table missing');
      
      console.log('🔧 Creating hipaa_scans table...');
      await client.query(`
        CREATE TABLE hipaa_scans (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          scan_id UUID NOT NULL,
          target_url TEXT NOT NULL,
          overall_score INTEGER NOT NULL,
          overall_passed BOOLEAN NOT NULL,
          compliance_level TEXT NOT NULL,
          analysis_levels_used JSONB NOT NULL,
          processing_time_ms INTEGER NOT NULL,
          total_checks INTEGER NOT NULL,
          passed_checks INTEGER NOT NULL,
          failed_checks INTEGER NOT NULL,
          critical_issues INTEGER DEFAULT 0,
          high_issues INTEGER DEFAULT 0,
          medium_issues INTEGER DEFAULT 0,
          low_issues INTEGER DEFAULT 0,
          scan_options JSONB,
          cache_hits INTEGER DEFAULT 0,
          errors JSONB,
          warnings JSONB,
          user_agent TEXT,
          scan_version TEXT DEFAULT '2.0',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `);
      console.log('✅ hipaa_scans table created!');
    } else {
      console.log('✅ hipaa_scans table exists');
    }

    console.log('🎉 Database setup complete!');

  } catch (error) {
    console.error('❌ Database test failed:', error);
  } finally {
    await client.end();
  }
}

quickTest();
