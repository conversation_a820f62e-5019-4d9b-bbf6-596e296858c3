const { Client } = require('pg');
require('dotenv').config();

async function createHipaaScansTables() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL || 'postgresql://complyuser:complypassword@localhost:5432/complychecker_dev'
  });

  try {
    await client.connect();
    console.log('✅ Connected to database');

    // Check if hipaa_scans table exists
    const checkTable = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'hipaa_scans'
      );
    `);

    if (checkTable.rows[0].exists) {
      console.log('✅ hipaa_scans table already exists');
      return;
    }

    console.log('🔄 Creating hipaa_scans table...');

    // Create the hipaa_scans table
    await client.query(`
      CREATE TABLE hipaa_scans (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        scan_id UUID REFERENCES scans(id) ON DELETE CASCADE NOT NULL,
        target_url TEXT NOT NULL,
        overall_score DECIMAL(5,2) NOT NULL,
        overall_passed BOOLEAN NOT NULL,
        compliance_level TEXT NOT NULL,
        analysis_levels_used JSONB NOT NULL,
        processing_time_ms INTEGER NOT NULL,
        total_checks INTEGER NOT NULL,
        passed_checks INTEGER NOT NULL,
        failed_checks INTEGER NOT NULL,
        critical_issues INTEGER DEFAULT 0,
        high_issues INTEGER DEFAULT 0,
        medium_issues INTEGER DEFAULT 0,
        low_issues INTEGER DEFAULT 0,
        scan_options JSONB,
        cache_hits INTEGER DEFAULT 0,
        errors JSONB,
        warnings JSONB,
        user_agent TEXT,
        scan_version TEXT DEFAULT '2.0',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
        completed_at TIMESTAMP WITH TIME ZONE
      );
    `);

    console.log('✅ hipaa_scans table created successfully');

    // Create indexes
    await client.query(`
      CREATE INDEX idx_hipaa_scans_scan_id ON hipaa_scans(scan_id);
      CREATE INDEX idx_hipaa_scans_target_url ON hipaa_scans(target_url);
      CREATE INDEX idx_hipaa_scans_overall_score ON hipaa_scans(overall_score);
      CREATE INDEX idx_hipaa_scans_compliance_level ON hipaa_scans(compliance_level);
      CREATE INDEX idx_hipaa_scans_created_at ON hipaa_scans(created_at);
    `);

    console.log('✅ Indexes created successfully');

  } catch (error) {
    console.error('❌ Error creating hipaa_scans table:', error.message);
  } finally {
    await client.end();
    console.log('🔌 Database connection closed');
  }
}

createHipaaScansTables();
