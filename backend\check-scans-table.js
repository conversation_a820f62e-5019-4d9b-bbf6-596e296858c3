const { Client } = require('pg');
require('dotenv').config();

async function checkScansTable() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL || 'postgresql://complyuser:complypassword@localhost:5432/complychecker_dev'
  });

  try {
    await client.connect();
    console.log('✅ Connected to database');

    // Check scans table structure
    const columns = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'scans'
      ORDER BY ordinal_position;
    `);
    
    console.log('📋 Scans table structure:');
    columns.rows.forEach(col => {
      console.log(`   - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? '(NOT NULL)' : '(NULLABLE)'} ${col.column_default ? `DEFAULT ${col.column_default}` : ''}`);
    });

    // Check if there are any users
    const users = await client.query('SELECT id, email FROM users LIMIT 1');
    if (users.rows.length > 0) {
      console.log(`\n👤 Found user: ${users.rows[0].email} (ID: ${users.rows[0].id})`);
    } else {
      console.log('\n👤 No users found in database');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await client.end();
  }
}

checkScansTable();
