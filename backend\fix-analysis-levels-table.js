const { Client } = require('pg');
require('dotenv').config();

async function fixAnalysisLevelsTable() {
  console.log('🔧 Fixing hipaa_analysis_levels table schema...');
  
  const client = new Client({
    connectionString: process.env.DATABASE_URL || 'postgresql://complyuser:complypassword@localhost:5432/complychecker_dev'
  });

  try {
    await client.connect();
    console.log('✅ Connected to database');

    // Check current table structure
    const currentColumns = await client.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'hipaa_analysis_levels'
      ORDER BY ordinal_position;
    `);

    const existingColumns = currentColumns.rows.map(row => row.column_name);
    console.log('📊 Current columns:', existingColumns);

    // Define required columns
    const requiredColumns = [
      'found_patterns', 'total_patterns', 'level_findings',
      'entities', 'privacy_statements', 'rights_statements',
      'compliance_gaps', 'risk_factors', 'ai_recommendations', 'positive_findings'
    ];

    const missingColumns = requiredColumns.filter(col => !existingColumns.includes(col));
    console.log('❌ Missing columns:', missingColumns);

    // Add missing columns one by one
    for (const column of missingColumns) {
      console.log(`🔧 Adding column: ${column}`);

      if (column === 'found_patterns' || column === 'total_patterns') {
        await client.query(`ALTER TABLE hipaa_analysis_levels ADD COLUMN IF NOT EXISTS ${column} INTEGER;`);
      } else {
        await client.query(`ALTER TABLE hipaa_analysis_levels ADD COLUMN IF NOT EXISTS ${column} JSONB;`);
      }
    }

    console.log('✅ All missing columns added!');

    console.log('🔧 Creating indexes...');
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_hipaa_analysis_levels_check_result_id ON hipaa_analysis_levels(check_result_id);
    `);
    
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_hipaa_analysis_levels_level ON hipaa_analysis_levels(level);
    `);

    console.log('✅ hipaa_analysis_levels table fixed with all required columns!');

    // Verify the table structure
    const columns = await client.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'hipaa_analysis_levels' 
      ORDER BY ordinal_position;
    `);

    console.log('📊 Table columns:');
    columns.rows.forEach(row => {
      console.log(`   - ${row.column_name}: ${row.data_type}`);
    });

    console.log('🎉 Table schema fix completed successfully!');

  } catch (error) {
    console.error('❌ Error fixing table:', error);
    throw error;
  } finally {
    await client.end();
  }
}

// Run immediately
fixAnalysisLevelsTable()
  .then(() => {
    console.log('✅ Schema fix complete! Privacy scans should now work.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Schema fix failed:', error);
    process.exit(1);
  });
