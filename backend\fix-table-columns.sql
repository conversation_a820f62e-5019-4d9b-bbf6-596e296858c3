-- Fix hipaa_analysis_levels table by adding missing columns
-- Run this SQL script to add all the missing columns that the privacy scan needs

-- Add Level 1 specific columns
ALTER TABLE hipaa_analysis_levels ADD COLUMN IF NOT EXISTS found_patterns INTEGER;
ALTER TABLE hipaa_analysis_levels ADD COLUMN IF NOT EXISTS total_patterns INTEGER;

-- Add Level 2 specific columns  
ALTER TABLE hipaa_analysis_levels ADD COLUMN IF NOT EXISTS entities JSONB;
ALTER TABLE hipaa_analysis_levels ADD COLUMN IF NOT EXISTS privacy_statements JSONB;
ALTER TABLE hipaa_analysis_levels ADD COLUMN IF NOT EXISTS rights_statements JSONB;

-- Add Level 3 specific columns
ALTER TABLE hipaa_analysis_levels ADD COLUMN IF NOT EXISTS compliance_gaps JSONB;
ALTER TABLE hipaa_analysis_levels ADD COLUMN IF NOT EXISTS risk_factors JSONB;
ALTER TABLE hipaa_analysis_levels ADD COLUMN IF NOT EXISTS ai_recommendations JSONB;
ALTER TABLE hipaa_analysis_levels ADD COLUMN IF NOT EXISTS positive_findings JSONB;

-- Add common column for all levels
ALTER TABLE hipaa_analysis_levels ADD COLUMN IF NOT EXISTS level_findings JSONB;

-- Verify the table structure
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'hipaa_analysis_levels' 
ORDER BY ordinal_position;
