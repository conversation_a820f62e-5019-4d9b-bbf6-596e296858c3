import dotenv from 'dotenv';
import express from 'express';
import cors from 'cors';
import { HipaaPrivacyPolicyOrchestrator } from './src/compliance/hipaa/privacy/services/privacy-orchestrator';
import { HipaaScanOptions } from './src/compliance/hipaa/privacy/types';

// Load environment variables
dotenv.config();

const app = express();
const port = process.env.BACKEND_PORT || 3001;

console.log('🚀 Starting real HIPAA backend server...');
console.log('📋 Environment:', process.env.NODE_ENV || 'development');
console.log('📋 Port:', port);

// Initialize HIPAA Privacy Orchestrator
const orchestrator = new HipaaPrivacyPolicyOrchestrator();

// Basic middleware
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check
app.get('/health', (req, res) => {
  console.log('📋 Health check requested');
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    services: {
      hipaaPrivacy: 'available',
      orchestrator: 'initialized'
    }
  });
});

// HIPAA Privacy scan endpoint with REAL integration
app.post('/api/v1/hipaa-privacy/scan', async (req, res) => {
  console.log('🚀 HIPAA Privacy scan request received');
  console.log('📋 Request body:', JSON.stringify(req.body, null, 2));
  
  try {
    const {
      targetUrl,
      timeout = 300000,
      maxRedirects = 5,
      userAgent = 'HIPAA-Privacy-Scanner/1.0',
      includeSubdomains = false,
      enableLevel1 = true,
      enableLevel2 = true,
      enableLevel3 = true,
      cacheResults = true,
      generateReport = true,
    } = req.body;

    if (!targetUrl) {
      return res.status(400).json({
        success: false,
        error: 'Missing targetUrl',
        message: 'targetUrl is required for privacy scan'
      });
    }

    // Validate URL format
    try {
      new URL(targetUrl);
    } catch (urlError) {
      return res.status(400).json({
        success: false,
        error: 'Invalid URL',
        message: 'Please provide a valid URL with protocol (http:// or https://)'
      });
    }

    console.log('🔍 Starting REAL HIPAA Privacy scan for:', targetUrl);
    console.log('⚙️ Scan options:', {
      timeout,
      maxRedirects,
      enableLevel1,
      enableLevel2,
      enableLevel3
    });

    const scanOptions: HipaaScanOptions = {
      timeout,
      maxRedirects,
      userAgent,
      includeSubdomains,
      enableLevel1,
      enableLevel2,
      enableLevel3,
      cacheResults,
      generateReport,
    };

    // Perform the REAL HIPAA privacy scan
    const startTime = Date.now();
    const result = await orchestrator.performComprehensiveScan(targetUrl, scanOptions);
    const processingTime = Date.now() - startTime;
    
    console.log('✅ REAL HIPAA Privacy scan completed successfully');
    console.log('📊 Scan results summary:', {
      overallScore: result.overallScore,
      overallPassed: result.overallPassed,
      totalChecks: result.checks.length,
      recommendations: result.recommendations.length,
      processingTime: `${processingTime}ms`
    });

    const scanId = `privacy-scan-${Date.now()}`;
    
    res.json({
      success: true,
      data: {
        scanId,
        status: 'completed',
        message: 'REAL HIPAA privacy scan completed successfully',
        result: result,
        metadata: {
          processingTime,
          scannerVersion: '1.0.0',
          realScan: true
        }
      }
    });

  } catch (error) {
    console.error('❌ Privacy scan error:', error);
    
    // Provide detailed error information
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    const errorStack = error instanceof Error ? error.stack : undefined;
    
    console.error('❌ Error details:', {
      message: errorMessage,
      stack: errorStack
    });

    res.status(500).json({
      success: false,
      error: 'Privacy scan failed',
      message: errorMessage,
      details: process.env.NODE_ENV === 'development' ? errorStack : undefined
    });
  }
});

// HIPAA Privacy scan status endpoint (for future async implementation)
app.get('/api/v1/hipaa-privacy/scan/:scanId/status', (req, res) => {
  const { scanId } = req.params;
  console.log('📋 Scan status requested for:', scanId);
  
  res.json({
    success: true,
    data: {
      scanId,
      status: 'completed',
      progress: 100,
      message: 'Privacy scans are currently synchronous and complete immediately'
    }
  });
});

// Error handling middleware
app.use((err: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('❌ Server error:', err);
  res.status(500).json({ 
    success: false,
    error: 'Internal server error', 
    message: err.message,
    details: process.env.NODE_ENV === 'development' ? err.stack : undefined
  });
});

// 404 handler
app.use((req, res) => {
  console.log(`❌ 404 - Route not found: ${req.method} ${req.url}`);
  res.status(404).json({ 
    success: false,
    error: 'Route not found', 
    path: req.url,
    availableEndpoints: [
      'GET /health',
      'POST /api/v1/hipaa-privacy/scan',
      'GET /api/v1/hipaa-privacy/scan/:scanId/status'
    ]
  });
});

// Start server
const server = app.listen(port, () => {
  console.log(`✅ REAL HIPAA backend server running on http://localhost:${port}`);
  console.log(`📋 Health check: http://localhost:${port}/health`);
  console.log(`🧪 Privacy scan: http://localhost:${port}/api/v1/hipaa-privacy/scan`);
  console.log('🚀 Ready to accept REAL HIPAA privacy scan requests!');
  console.log('🔍 Using REAL HipaaPrivacyPolicyOrchestrator for scanning');
}).on('error', (err: NodeJS.ErrnoException) => {
  console.error('❌ Failed to start server:', err);
  if (err.code === 'EADDRINUSE') {
    console.error(`❌ Port ${port} is already in use`);
    console.log('💡 Try killing existing processes or use a different port');
  }
  process.exit(1);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('📋 SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('📋 SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

export default app;
