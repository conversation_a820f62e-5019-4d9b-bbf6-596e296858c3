const { Client } = require('pg');
const fs = require('fs');
require('dotenv').config();

async function runSqlFix() {
  console.log('🔧 Running SQL fix for hipaa_analysis_levels table...');
  
  const client = new Client({
    connectionString: process.env.DATABASE_URL || 'postgresql://complyuser:complypassword@localhost:5432/complychecker_dev'
  });

  try {
    await client.connect();
    console.log('✅ Connected to database');

    // Read and execute the SQL file
    const sqlContent = fs.readFileSync('./fix-table-columns.sql', 'utf8');
    const statements = sqlContent.split(';').filter(stmt => stmt.trim() && !stmt.trim().startsWith('--'));

    for (const statement of statements) {
      if (statement.trim()) {
        console.log('📝 Executing:', statement.trim().substring(0, 50) + '...');
        const result = await client.query(statement.trim());
        
        // If it's a SELECT statement, show results
        if (statement.trim().toUpperCase().startsWith('SELECT')) {
          console.log('📊 Table columns:');
          result.rows.forEach(row => {
            console.log(`   - ${row.column_name}: ${row.data_type}`);
          });
        }
      }
    }

    console.log('✅ All SQL statements executed successfully!');
    console.log('🎉 hipaa_analysis_levels table is now ready for privacy scans!');

  } catch (error) {
    console.error('❌ Error running SQL fix:', error);
  } finally {
    await client.end();
  }
}

runSqlFix();
