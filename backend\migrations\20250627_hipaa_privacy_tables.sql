-- HIPAA Privacy Scan Tables Migration
-- This creates the missing tables for HIPAA privacy scan functionality

-- Create HIPAA Scans table for detailed scan tracking
CREATE TABLE IF NOT EXISTS hipaa_scans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    scan_id UUID REFERENCES scans(id) ON DELETE CASCADE NOT NULL,
    target_url TEXT NOT NULL,
    overall_score INTEGER NOT NULL, -- 0-100 compliance score
    overall_passed BOOLEAN NOT NULL,
    compliance_level TEXT NOT NULL, -- compliant, mostly_compliant, partially_compliant, non_compliant
    analysis_levels_used JSONB NOT NULL, -- [1, 2, 3] - which levels were executed
    processing_time_ms INTEGER NOT NULL,
    total_checks INTEGER NOT NULL,
    passed_checks INTEGER NOT NULL,
    failed_checks INTEGER NOT NULL,
    critical_issues INTEGER DEFAULT 0,
    high_issues INTEGER DEFAULT 0,
    medium_issues INTEGER DEFAULT 0,
    low_issues INTEGER DEFAULT 0,
    scan_options JSONB,
    cache_hits INTEGER DEFAULT 0,
    errors JSONB,
    warnings JSONB,
    user_agent TEXT,
    scan_version TEXT DEFAULT '2.0',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create HIPAA Check Results table for individual check details
CREATE TABLE IF NOT EXISTS hipaa_check_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    hipaa_scan_id UUID REFERENCES hipaa_scans(id) ON DELETE CASCADE NOT NULL,
    check_id TEXT NOT NULL, -- e.g., 'HIPAA-PP-001'
    name TEXT NOT NULL,
    category TEXT NOT NULL, -- presence, accessibility, content_structure, etc.
    passed BOOLEAN NOT NULL,
    severity TEXT NOT NULL, -- critical, high, medium, low, info
    confidence INTEGER NOT NULL, -- 0-100 confidence score
    description TEXT NOT NULL,
    overall_score INTEGER, -- 0-100 combined score from all levels
    processing_time_ms INTEGER NOT NULL,
    check_version TEXT DEFAULT '2.0',
    analysis_levels_executed JSONB, -- [1, 2, 3] - which levels ran for this check
    errors JSONB, -- Array of errors during check execution
    warnings JSONB, -- Array of warnings during check execution
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create HIPAA Findings table for detailed findings from each check
CREATE TABLE IF NOT EXISTS hipaa_findings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    check_result_id UUID REFERENCES hipaa_check_results(id) ON DELETE CASCADE NOT NULL,
    type TEXT NOT NULL, -- missing_content, exact_match, context_match, etc.
    location TEXT NOT NULL, -- Position or section where finding was made
    content TEXT NOT NULL, -- The actual content found or missing
    severity TEXT NOT NULL, -- critical, high, medium, low, info
    message TEXT NOT NULL, -- Human-readable description
    confidence INTEGER NOT NULL, -- 0-100 confidence in this finding
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create HIPAA Analysis Levels table for storing 3-level analysis results
CREATE TABLE IF NOT EXISTS hipaa_analysis_levels (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    check_result_id UUID REFERENCES hipaa_check_results(id) ON DELETE CASCADE NOT NULL,
    level INTEGER NOT NULL, -- 1, 2, or 3
    method TEXT NOT NULL, -- 'Basic Phrase Matching', 'NLP with Compromise.js', 'AI Analysis with DistilBERT'
    score INTEGER NOT NULL, -- 0-100 score for this level
    confidence INTEGER NOT NULL, -- 0-100 confidence for this level
    processing_time_ms INTEGER NOT NULL,
    findings_count INTEGER DEFAULT 0,
    errors JSONB,
    warnings JSONB,
    metadata JSONB, -- Additional level-specific metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create HIPAA Remediation table for detailed remediation guidance
CREATE TABLE IF NOT EXISTS hipaa_remediation (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    check_result_id UUID REFERENCES hipaa_check_results(id) ON DELETE CASCADE NOT NULL,
    priority TEXT NOT NULL, -- critical, high, medium, low
    effort TEXT NOT NULL, -- minimal, moderate, significant, extensive
    steps JSONB NOT NULL, -- Array of remediation steps
    resources JSONB NOT NULL, -- Array of helpful resources
    timeline TEXT NOT NULL, -- Estimated timeline for remediation
    estimated_cost TEXT, -- Optional cost estimate
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create HIPAA Evidence table for storing evidence collected during analysis
CREATE TABLE IF NOT EXISTS hipaa_evidence (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    check_result_id UUID REFERENCES hipaa_check_results(id) ON DELETE CASCADE NOT NULL,
    type TEXT NOT NULL, -- text_excerpt, link, screenshot, metadata
    content TEXT NOT NULL, -- The actual evidence content
    location TEXT NOT NULL, -- Where the evidence was found
    relevance INTEGER NOT NULL, -- 0-100 relevance score
    evidence_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create HIPAA Recommendations table for storing actionable recommendations
CREATE TABLE IF NOT EXISTS hipaa_recommendations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    hipaa_scan_id UUID REFERENCES hipaa_scans(id) ON DELETE CASCADE NOT NULL,
    recommendation_id TEXT NOT NULL,
    priority TEXT NOT NULL, -- critical, high, medium, low
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    category TEXT NOT NULL, -- content, structure, accessibility, etc.
    effort TEXT NOT NULL, -- minimal, moderate, significant, extensive
    impact TEXT NOT NULL, -- high, medium, low
    timeline TEXT NOT NULL, -- immediate, short_term, medium_term, long_term
    resources JSONB, -- Array of helpful resources
    related_checks JSONB, -- Array of related check IDs
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create HIPAA Content Analysis table for storing content analysis results
CREATE TABLE IF NOT EXISTS hipaa_content_analysis (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    hipaa_scan_id UUID REFERENCES hipaa_scans(id) ON DELETE CASCADE NOT NULL,
    content_url TEXT NOT NULL,
    content_type TEXT, -- text/html, application/pdf, etc.
    language TEXT, -- en, es, fr, etc.
    content_length INTEGER NOT NULL,
    sections_found INTEGER NOT NULL,
    patterns_matched INTEGER NOT NULL,
    entities_extracted INTEGER NOT NULL,
    readability_score DECIMAL(5,2), -- Flesch reading ease score
    organization_score DECIMAL(5,2), -- 0-100 organization quality score
    has_proper_headings BOOLEAN DEFAULT FALSE,
    heading_levels JSONB, -- Array of heading levels found
    average_section_length DECIMAL(8,2),
    privacy_policy_links JSONB, -- Array of discovered privacy policy links
    structure_analysis JSONB, -- Detailed structure analysis results
    page_title TEXT,
    last_modified TIMESTAMP WITH TIME ZONE, -- When the content was last modified
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_hipaa_scans_scan_id ON hipaa_scans(scan_id);
CREATE INDEX IF NOT EXISTS idx_hipaa_scans_target_url ON hipaa_scans(target_url);
CREATE INDEX IF NOT EXISTS idx_hipaa_scans_overall_score ON hipaa_scans(overall_score);
CREATE INDEX IF NOT EXISTS idx_hipaa_scans_created_at ON hipaa_scans(created_at);

CREATE INDEX IF NOT EXISTS idx_hipaa_check_results_hipaa_scan_id ON hipaa_check_results(hipaa_scan_id);
CREATE INDEX IF NOT EXISTS idx_hipaa_check_results_check_id ON hipaa_check_results(check_id);
CREATE INDEX IF NOT EXISTS idx_hipaa_check_results_category ON hipaa_check_results(category);
CREATE INDEX IF NOT EXISTS idx_hipaa_check_results_passed ON hipaa_check_results(passed);
CREATE INDEX IF NOT EXISTS idx_hipaa_check_results_severity ON hipaa_check_results(severity);
CREATE INDEX IF NOT EXISTS idx_hipaa_check_results_confidence ON hipaa_check_results(confidence);

CREATE INDEX IF NOT EXISTS idx_hipaa_findings_check_result_id ON hipaa_findings(check_result_id);
CREATE INDEX IF NOT EXISTS idx_hipaa_findings_type ON hipaa_findings(type);
CREATE INDEX IF NOT EXISTS idx_hipaa_findings_severity ON hipaa_findings(severity);

CREATE INDEX IF NOT EXISTS idx_hipaa_analysis_levels_check_result_id ON hipaa_analysis_levels(check_result_id);
CREATE INDEX IF NOT EXISTS idx_hipaa_analysis_levels_level ON hipaa_analysis_levels(level);

CREATE INDEX IF NOT EXISTS idx_hipaa_remediation_check_result_id ON hipaa_remediation(check_result_id);
CREATE INDEX IF NOT EXISTS idx_hipaa_remediation_priority ON hipaa_remediation(priority);

CREATE INDEX IF NOT EXISTS idx_hipaa_evidence_check_result_id ON hipaa_evidence(check_result_id);
CREATE INDEX IF NOT EXISTS idx_hipaa_evidence_type ON hipaa_evidence(type);

CREATE INDEX IF NOT EXISTS idx_hipaa_recommendations_hipaa_scan_id ON hipaa_recommendations(hipaa_scan_id);
CREATE INDEX IF NOT EXISTS idx_hipaa_recommendations_priority ON hipaa_recommendations(priority);

CREATE INDEX IF NOT EXISTS idx_hipaa_content_analysis_hipaa_scan_id ON hipaa_content_analysis(hipaa_scan_id);
CREATE INDEX IF NOT EXISTS idx_hipaa_content_analysis_content_url ON hipaa_content_analysis(content_url);
CREATE INDEX IF NOT EXISTS idx_hipaa_content_analysis_readability_score ON hipaa_content_analysis(readability_score);
CREATE INDEX IF NOT EXISTS idx_hipaa_content_analysis_organization_score ON hipaa_content_analysis(organization_score);
