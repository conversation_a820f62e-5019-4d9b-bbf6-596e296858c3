const { Client } = require('pg');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

async function runPrivacyMigration() {
  console.log('🔄 Running HIPAA Privacy Tables Migration...');
  
  const client = new Client({
    connectionString: process.env.DATABASE_URL || 'postgresql://complyuser:complypassword@localhost:5432/complychecker_dev'
  });

  try {
    await client.connect();
    console.log('✅ Connected to database');

    // Read the migration file
    const migrationPath = path.join(__dirname, 'migrations', '20250627_hipaa_privacy_tables.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    console.log('📝 Executing migration...');
    await client.query(migrationSQL);

    console.log('✅ HIPAA Privacy tables migration completed successfully!');

    // Verify tables were created
    console.log('🔍 Verifying tables...');
    const tables = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name LIKE 'hipaa_%'
      ORDER BY table_name
    `);

    console.log('📊 HIPAA tables found:');
    tables.rows.forEach(row => {
      console.log(`   - ${row.table_name}`);
    });

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await client.end();
  }
}

// Run the migration
if (require.main === module) {
  runPrivacyMigration()
    .then(() => {
      console.log('🎉 Migration completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Migration failed:', error);
      process.exit(1);
    });
}

module.exports = { runPrivacyMigration };
